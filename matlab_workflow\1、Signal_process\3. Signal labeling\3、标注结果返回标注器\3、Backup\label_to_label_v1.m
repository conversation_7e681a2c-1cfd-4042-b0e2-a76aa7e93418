function [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc, varargin)
%LABEL_TO_LABEL 从标注文件中提取指定文件的标注信息
%   从已有的标注文件中查找指定文件的标注信息，提取标签值和时间位置，
%   并进行统计分析。主要用于肠鸣音信号分析中的标注数据提取。
%
%   Syntax:
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc)
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc, Fs)
%
%   Inputs:
%   x - 信号数据 (double array, 当前版本未使用)
%   t - 时间数据 (double array, 当前版本未使用)
%   parentLabelVal - 父标签值 (string/char, 当前版本未使用)
%   parentLabelLoc - 父标签位置 (double array, 当前版本未使用)
%   Fs - 采样率 (double, 可选参数, 默认值: 2570 Hz)
%
%   Outputs:
%   labelVals - 标签值字符串数组 (string array, 包含SB/MB/CRS等标注类型)
%   labelLocs - 标签时间位置矩阵 (double array, Nx2, [开始时间, 结束时间])
%
%   Example:
%   % 基本用法
%   [labels, locations] = label_to_label([], [], [], []);
%
%   % 指定采样率
%   [labels, locations] = label_to_label([], [], [], [], 2570);
%
%   Algorithm:
%   1. 加载标注文件'上午第二次.mat'，获取标注信息表
%   2. 遍历文件列表，查找目标文件'data7_5min_tt'
%   3. 提取目标文件的标注值(SB/MB/CRS)和时间范围
%   4. 统计各类型标注数量并显示详细信息
%   5. 返回格式化的标签值和位置数组
%
%   Notes:
%   - 需要确保标注文件'上午第二次.mat'存在于当前目录
%   - 目标文件名'data7_5min_tt'为硬编码，建议后续版本改为参数
%   - 当前版本的前四个输入参数未被使用，保留是为了接口兼容性
%   - 支持的标注类型：SB(单次肠鸣音)、MB(多次肠鸣音)、CRS(持续肠鸣音)
%
%   See also: LOAD, ISTABLE, FILEPARTS
    
    % 初始化输出变量
    labelVals = string([]);
    labelLocs = zeros(0,2);
    
    % 设置默认采样率
    if nargin < 5
        Fs = 2570;
    else
        Fs = varargin{1};
    end
    
    % 指定要查找的目标文件名
    targetFile = 'data7_5min_tt';
    
    try
        % 加载标注文件
        load('上午第二次.mat');
        
        % 获取 Labels
        labels = ls.Labels;
        
        % 提取文件路径列和 BS 表
        filePaths = labels.Row;
        bsTables = labels{:, 1};
        
        % 遍历每个文件，查找目标文件
        found = false;
        for i = 1:numel(bsTables)
            % 获取当前文件路径和文件名
            currentFilePath = filePaths{i};
            [~, fileName, ~] = fileparts(currentFilePath);
            
            % 检查是否为目标文件
            if contains(fileName, targetFile)
                currentBsTable = bsTables{i};
                
                % 验证当前 BS 表是否为 table
                if istable(currentBsTable)
                    % 打印文件信息
                    fprintf('\n找到目标文件: %s\n', fileName);
                    fprintf('文件路径: %s\n', currentFilePath);
                    fprintf('标注总数: %d\n\n', height(currentBsTable));
                    
                    % 打印标注详细信息
                    fprintf('标注详情:\n');
                    fprintf('序号\t类型\t开始时间(s)\t结束时间(s)\t持续时间(ms)\n');
                    fprintf('----------------------------------------------------\n');
                    
                    % 统计各类型数量
                    sb_count = 0;
                    mb_count = 0;
                    crs_count = 0;
                    
                    % 获取所有标注值和位置
                    values = table2array(currentBsTable(:, 'Value'));
                    locations = table2array(currentBsTable(:, 'ROILimits'));
                    
                    % 遍历并处理每个标注
                    for j = 1:height(currentBsTable)
                        % 获取当前标注信息
                        roiLimits = locations(j, :);
                        labelValue = values(j);
                        
                        % 计算持续时间（毫秒）
                        duration_ms = (roiLimits(2) - roiLimits(1)) * 1000;
                        
                        % 打印标注信息
                        fprintf('%d\t%s\t%.3f\t\t%.3f\t\t%.1f\n', ...
                            j, labelValue, roiLimits(1), roiLimits(2), duration_ms);
                        
                        % 更新计数
                        switch labelValue
                            case 'SB'
                                sb_count = sb_count + 1;
                            case 'MB'
                                mb_count = mb_count + 1;
                            case 'CRS'
                                crs_count = crs_count + 1;
                        end
                    end
                    
                    % 打印统计信息
                    fprintf('\n标注统计:\n');
                    fprintf('SB (单次肠鸣音): %d\n', sb_count);
                    fprintf('MB (多次肠鸣音): %d\n', mb_count);
                    fprintf('CRS (持续肠鸣音): %d\n', crs_count);
                    fprintf('总计: %d\n', height(currentBsTable));
                    
                    % 设置返回值
                    labelVals = string(values);
                    labelLocs = locations;
                    found = true;
                    break;
                end
            end
        end
        
        % 如果没有找到目标文件
        if ~found
            warning('未找到文件 %s 的标注信息。', targetFile);
        end
        
    catch ME
        % 错误处理
        warning(ME.identifier, '处理过程出现错误：%s', ME.message);
        disp(['错误位置: ' ME.stack(1).name '，第 ' num2str(ME.stack(1).line) ' 行']);
    end
    
    % 确保输出格式正确
    if isempty(labelVals)
        labelVals = string([]);
        labelLocs = zeros(0,2);
    elseif iscell(labelVals)
        labelVals = string(labelVals);
    end
end