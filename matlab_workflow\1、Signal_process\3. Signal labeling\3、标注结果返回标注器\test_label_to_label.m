%% 测试修改后的 label_to_label 函数
% 测试脚本用于验证 label_to_label 函数的各种功能
% 包括参数化、路径处理、双通道支持等改进

%% 清理环境
clear; clc; close all;

fprintf('=== label_to_label 函数测试 ===\n\n');

%% 测试1: 基本功能测试（使用默认参数）
fprintf('测试1: 基本功能测试（使用默认参数）\n');
fprintf('调用: label_to_label([], [], [], [])\n');
fprintf('预期: 使用默认的标注文件和目标文件\n');
fprintf('----------------------------------------\n');

try
    [labelVals1, labelLocs1] = label_to_label([], [], [], []);
    fprintf('✓ 基本功能测试通过\n');
    fprintf('  返回标签数量: %d\n', length(labelVals1));
    if ~isempty(labelVals1)
        fprintf('  标签类型: %s\n', strjoin(unique(labelVals1), ', '));
    end
catch ME
    fprintf('❌ 基本功能测试失败: %s\n', ME.message);
end

fprintf('\n');

%% 测试2: 指定采样率测试
fprintf('测试2: 指定采样率测试\n');
fprintf('调用: label_to_label([], [], [], [], 2570)\n');
fprintf('预期: 使用指定采样率，其他参数使用默认值\n');
fprintf('----------------------------------------\n');

try
    [labelVals2, labelLocs2] = label_to_label([], [], [], [], 2570);
    fprintf('✓ 采样率参数测试通过\n');
    fprintf('  返回标签数量: %d\n', length(labelVals2));
catch ME
    fprintf('❌ 采样率参数测试失败: %s\n', ME.message);
end

fprintf('\n');

%% 测试3: 指定标注文件测试
fprintf('测试3: 指定标注文件测试\n');
fprintf('调用: label_to_label([], [], [], [], 2570, ''ls_data3.mat'')\n');
fprintf('预期: 使用指定的标注文件\n');
fprintf('----------------------------------------\n');

try
    [labelVals3, labelLocs3] = label_to_label([], [], [], [], 2570, 'ls_data3.mat');
    fprintf('✓ 标注文件参数测试通过\n');
    fprintf('  返回标签数量: %d\n', length(labelVals3));
catch ME
    fprintf('❌ 标注文件参数测试失败: %s\n', ME.message);
end

fprintf('\n');

%% 测试4: 指定目标文件测试
fprintf('测试4: 指定目标文件测试\n');
fprintf('调用: label_to_label([], [], [], [], 2570, ''ls_data3.mat'', ''data3_5min_seg001_tt'')\n');
fprintf('预期: 使用指定的目标文件\n');
fprintf('----------------------------------------\n');

try
    [labelVals4, labelLocs4] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data3_5min_seg001_tt');
    fprintf('✓ 目标文件参数测试通过\n');
    fprintf('  返回标签数量: %d\n', length(labelVals4));
catch ME
    fprintf('❌ 目标文件参数测试失败: %s\n', ME.message);
end

fprintf('\n');

%% 测试5: 双通道文件名测试
fprintf('测试5: 双通道文件名测试\n');
fprintf('测试双通道文件名的匹配逻辑\n');
fprintf('----------------------------------------\n');

% 测试不同的双通道文件名格式
testTargets = {
    'data1_5min_seg001_tt1',
    'data1_5min_seg001_tt2', 
    'data1_5min_seg001_tt',
    'data2_5min_seg002_tt'
};

for i = 1:length(testTargets)
    targetFile = testTargets{i};
    fprintf('测试目标文件: %s\n', targetFile);
    
    try
        [labelVals, labelLocs] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', targetFile);
        fprintf('  ✓ 成功，返回标签数量: %d\n', length(labelVals));
    catch ME
        fprintf('  ❌ 失败: %s\n', ME.message);
    end
end

fprintf('\n');

%% 测试6: 错误处理测试
fprintf('测试6: 错误处理测试\n');
fprintf('测试各种错误情况的处理\n');
fprintf('----------------------------------------\n');

% 测试不存在的标注文件
fprintf('6.1 测试不存在的标注文件\n');
try
    [labelVals6, labelLocs6] = label_to_label([], [], [], [], 2570, 'nonexistent.mat');
    fprintf('  ❌ 应该抛出错误但没有\n');
catch ME
    fprintf('  ✓ 正确捕获错误: %s\n', ME.message);
end

% 测试不存在的目标文件
fprintf('6.2 测试不存在的目标文件\n');
try
    [labelVals7, labelLocs7] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'nonexistent_file');
    fprintf('  ✓ 处理不存在的目标文件，返回标签数量: %d\n', length(labelVals7));
catch ME
    fprintf('  ✓ 正确处理错误: %s\n', ME.message);
end

fprintf('\n');

%% 测试7: 参数类型验证测试
fprintf('测试7: 参数类型验证测试\n');
fprintf('测试参数类型验证功能\n');
fprintf('----------------------------------------\n');

% 测试错误的参数类型
fprintf('7.1 测试数值类型的标注文件名\n');
try
    [labelVals8, labelLocs8] = label_to_label([], [], [], [], 2570, 123);
    fprintf('  ❌ 应该抛出错误但没有\n');
catch ME
    fprintf('  ✓ 正确捕获参数类型错误: %s\n', ME.message);
end

fprintf('7.2 测试数值类型的目标文件名\n');
try
    [labelVals9, labelLocs9] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 456);
    fprintf('  ❌ 应该抛出错误但没有\n');
catch ME
    fprintf('  ✓ 正确捕获参数类型错误: %s\n', ME.message);
end

fprintf('\n');

%% 测试总结
fprintf('=== 测试总结 ===\n');
fprintf('所有测试已完成。请检查上述输出以确认函数的各项功能是否正常工作。\n');
fprintf('主要改进功能:\n');
fprintf('  ✓ 参数化硬编码值\n');
fprintf('  ✓ 修正文件路径处理\n');
fprintf('  ✓ 双通道文件名支持\n');
fprintf('  ✓ 向后兼容性\n');
fprintf('  ✓ 改进错误处理\n');
fprintf('  ✓ 参数验证\n');
