# label_to_label.m 函数改进说明

## 改进概述

本次对 `label_to_label.m` 函数进行了全面改进，主要解决了硬编码、路径处理和双通道支持等问题，同时保持了向后兼容性。

## 主要改进内容

### 1. 参数化硬编码值 ✅

**改进前：**
- 标注文件名硬编码为 `'上午第二次.mat'`
- 目标文件名硬编码为 `'data7_5min_tt'`

**改进后：**
- 支持通过参数指定标注文件名（默认：`'ls_data3.mat'`）
- 支持通过参数指定目标文件名（默认：`'data7_5min_tt'`）
- 新的函数签名支持更多参数：
  ```matlab
  [labelVals, labelLocs] = label_to_label(~, ~, ~, ~, Fs, labelFileName, targetFile)
  ```

### 2. 修正文件路径处理 ✅

**改进前：**
- 假设标注文件在当前目录
- 使用 `load('上午第二次.mat')`

**改进后：**
- 标注文件从 `4、Label/` 文件夹加载
- 使用 `fullfile('4、Label', labelFileName)` 构建完整路径
- 添加文件存在性检查
- 验证标注文件的数据结构（`ls.Labels`）

### 3. 双通道文件名支持 ✅

**改进前：**
- 简单的字符串匹配：`contains(fileName, targetFile)`

**改进后：**
- 智能的双通道文件名匹配逻辑
- 支持 `data1_5min_seg001_tt1` 和 `data1_5min_seg001_tt2` 格式
- 使用正则表达式处理通道后缀：
  ```matlab
  baseFileName = regexprep(fileName, '(tt1|tt2)$', 'tt');
  targetFileBase = regexprep(targetFile, '(tt1|tt2)$', 'tt');
  ```
- 支持模糊匹配，提高文件查找的灵活性

### 4. 向后兼容性保持 ✅

**兼容性措施：**
- 保持原有的函数签名结构
- 前四个参数标记为 `~`（未使用但保留）
- 所有新参数都有默认值
- 现有调用方式完全兼容：
  ```matlab
  % 原有调用方式仍然有效
  [labels, locations] = label_to_label([], [], [], []);
  ```

### 5. 改进错误处理 ✅

**新增错误处理功能：**
- 文件存在性检查
- 数据结构验证（`ls` 结构体和 `Labels` 字段）
- 参数类型验证
- 详细的错误信息和建议
- 友好的状态报告和进度显示

**错误信息示例：**
```
❌ 处理过程出现错误
  错误信息: 标注文件不存在: 4、Label/nonexistent.mat
  建议: 检查标注文件路径和文件名是否正确
```

### 6. 增强的输出信息 ✅

**改进的输出格式：**
- 使用图标（✓、❌、⚠）提高可读性
- 显示文件匹配详情（基础文件名、原始路径）
- 支持其他标注类型的统计
- 最终状态报告
- 可用文件列表显示（当目标文件未找到时）

## 新的使用方式

### 基本用法（向后兼容）
```matlab
% 使用默认参数
[labels, locations] = label_to_label([], [], [], []);
```

### 指定采样率
```matlab
[labels, locations] = label_to_label([], [], [], [], 2570);
```

### 指定标注文件
```matlab
[labels, locations] = label_to_label([], [], [], [], 2570, 'ls_data3.mat');
```

### 完全自定义
```matlab
[labels, locations] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data1_5min_seg001_tt1');
```

### 双通道文件处理
```matlab
% 以下调用都能正确匹配到同一个基础文件
[labels1, locs1] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data1_5min_seg001_tt');
[labels2, locs2] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data1_5min_seg001_tt1');
[labels3, locs3] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data1_5min_seg001_tt2');
```

## 测试验证

提供了完整的测试脚本 `test_label_to_label.m`，包含以下测试：

1. **基本功能测试** - 验证默认参数工作
2. **参数传递测试** - 验证各种参数组合
3. **双通道支持测试** - 验证双通道文件名匹配
4. **错误处理测试** - 验证各种错误情况
5. **参数验证测试** - 验证参数类型检查

## 文件结构要求

确保以下文件结构：
```
3、标注结果返回标注器/
├── label_to_label.m          # 改进后的主函数
├── test_label_to_label.m     # 测试脚本
├── 4、Label/                 # 标注文件目录
│   └── ls_data3.mat         # 标注数据文件
└── 1、Raw data/              # 原始数据目录
    └── *.mat                # 原始信号文件
```

## 注意事项

1. **标注文件格式**：确保标注文件包含正确的 `ls.Labels` 结构
2. **文件命名**：支持标准的双通道命名规则（tt、tt1、tt2）
3. **路径依赖**：函数假设从包含 `4、Label/` 文件夹的目录运行
4. **兼容性**：完全向后兼容，现有代码无需修改

## 性能改进

- 添加了文件存在性预检查，避免不必要的加载操作
- 改进了文件匹配算法，支持更灵活的查找
- 优化了错误处理流程，提供更快的故障诊断

## 未来扩展建议

1. 支持批量处理多个目标文件
2. 添加标签类型过滤功能
3. 支持自定义标注文件夹路径
4. 添加数据可视化功能
5. 支持导出统计报告功能
